{
  "compilerOptions": {
    "target": "ES2020",
    "module": "NodeNext", // Update module to support import assertions
    "moduleResolution": "NodeNext", // Update moduleResolution accordingly
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "declaration": true,
    "sourceMap": true,
    "lib": ["ES2020"],
    "baseUrl": ".",
    "paths": {
      "*": ["node_modules/*"]
    },
    "resolveJsonModule": true
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist", "src/__tests__"]
}