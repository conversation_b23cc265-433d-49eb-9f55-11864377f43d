{"ALLUSERSPROFILE": "C:\\ProgramData", "AMDRMSDKPATH": "C:\\Program Files\\AMD\\RyzenMasterSDK\\", "APPDATA": "C:\\Users\\<USER>\\AppData\\Roaming", "COLOR": "1", "CommonProgramFiles": "C:\\Program Files\\Common Files", "CommonProgramFiles(x86)": "C:\\Program Files (x86)\\Common Files", "CommonProgramW6432": "C:\\Program Files\\Common Files", "COMPUTERNAME": "DESKTOP-O4OOE4K", "ComSpec": "C:\\WINDOWS\\system32\\cmd.exe", "CUDA_PATH": "D:\\CUDA", "CUDA_PATH_V12_5": "D:\\CUDA", "DriverData": "C:\\Windows\\System32\\Drivers\\DriverData", "EDITOR": "C:\\WINDOWS\\notepad.exe", "FIREBIRD_DATABASE": "F:\\Proyectos\\SAI\\EMPLOYEE.FDB", "FIREBIRD_HOST": "localhost", "FIREBIRD_PASSWORD": "masterkey", "FIREBIRD_PORT": "3050", "FIREBIRD_ROLE": "", "FIREBIRD_USER": "SYSDBA", "FPS_BROWSER_APP_PROFILE_STRING": "Internet Explorer", "FPS_BROWSER_USER_PROFILE_STRING": "<PERSON><PERSON><PERSON>", "GPU_MAX_ALLOC_PERCENT": "100", "GPU_MAX_HEAP_SIZE": "100", "GPU_MAX_SINGLE_ALLOC_PERCENT": "100", "GPU_MAX_USE_SYNC_OBJECTS": "1", "GPU_SINGLE_ALLOC_PERCENT": "100", "HOME": "C:\\Users\\<USER>", "HOMEDRIVE": "C:", "HOMEPATH": "\\Users\\jhonn", "INIT_CWD": "D:\\serverN8N\\MCP\\Firebird", "LOCALAPPDATA": "C:\\Users\\<USER>\\AppData\\Local", "LOGONSERVER": "\\\\DESKTOP-O4OOE4K", "LOG_LEVEL": "debug", "MAX_CONNECTIONS": "50", "NODE": "D:\\Program Files\\nodejs\\node.exe", "npm_command": "run-script", "npm_config_cache": "C:\\Users\\<USER>\\AppData\\Local\\npm-cache", "npm_config_globalconfig": "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc", "npm_config_global_prefix": "C:\\Users\\<USER>\\AppData\\Roaming\\npm", "npm_config_init_module": "C:\\Users\\<USER>\\.npm-init.js", "npm_config_local_prefix": "D:\\serverN8N\\MCP\\Firebird", "npm_config_node_gyp": "D:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js", "npm_config_noproxy": "", "npm_config_npm_version": "10.9.2", "npm_config_prefix": "C:\\Users\\<USER>\\AppData\\Roaming\\npm", "npm_config_userconfig": "C:\\Users\\<USER>\\.npmrc", "npm_config_user_agent": "npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false", "npm_execpath": "D:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js", "npm_lifecycle_event": "inspector", "npm_lifecycle_script": "npm run build && node run-inspector.cjs", "npm_node_execpath": "D:\\Program Files\\nodejs\\node.exe", "npm_package_bin_mcp-firebird": "dist/cli.js", "npm_package_json": "D:\\serverN8N\\MCP\\Firebird\\package.json", "npm_package_name": "mcp-firebird", "npm_package_version": "2.0.5-alpha.3", "NUMBER_OF_PROCESSORS": "12", "OneDrive": "C:\\Users\\<USER>\\OneDrive", "OPENSSL_CONF": "C:\\OpenSSL-Win32\\bin\\openssl.cfg", "OS": "Windows_NT", "Path": "D:\\serverN8N\\MCP\\Firebird\\node_modules\\.bin;D:\\serverN8N\\MCP\\node_modules\\.bin;D:\\serverN8N\\node_modules\\.bin;D:\\node_modules\\.bin;D:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;D:\\Program Files\\PowerShell\\7;D:\\CUDA\\bin;D:\\CUDA\\libnvvp;d:\\Users\\jhonn\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;D:\\Program Files (x86)\\dxgettext;D:\\Program Files\\Python311\\Scripts\\;D:\\Program Files\\Python311\\;C:\\Program Files\\Eclipse Adoptium\\jdk-**********-hotspot\\bin;C:\\Program Files (x86)\\Embarcadero\\Studio\\22.0\\bin;C:\\Program Files (x86)\\Embarcadero\\Studio\\22.0\\bin64;F:\\Crystal\\Common\\3.5\\bin\\NOTES\\;F:\\Crystal\\Common\\3.5\\bin\\NOTES\\DATA\\;F:\\app\\client\\jhonn\\product\\18.0.0\\client_1\\bin;C:\\app\\jhonn\\product\\18.0.0\\dbhomeXE\\bin;C:\\Program Files\\Common Files\\Oracle\\Java\\javapath;C:\\Program Files\\AdoptOpenJDK\\jdk-**********-hotspot\\bin;F:\\Delphi\\bin;C:\\Users\\<USER>\\Documents\\Embarcadero\\Studio\\22.0\\Bpl;F:\\Delphi\\bin64;C:\\Users\\<USER>\\Documents\\Embarcadero\\Studio\\22.0\\Bpl\\Win64;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;F:\\Windows Kits\\10\\Windows Performance Toolkit\\;D:\\Flutter\\flutter\\bin;C:\\Program Files\\Git\\cmd;G:\\Program Files\\PuTTY\\;C:\\Program Files\\Microsoft SQL Server\\;D:\\Program Files\\nodejs\\;D:\\Program Files\\PowerShell\\7\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2024.2.0\\;e:\\Users\\jhonn\\AppData\\Local\\Programs\\Trae\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;E:\\Sophos\\Sophos SSL VPN Client\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Program Files (x86)\\sox-14-4-2;C:\\ffmpeg\\bin;D:\\Users\\jhonn\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;D:\\Users\\jhonn\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;D:\\CUDA;D:\\CUDA\\compute-sanitizer;D:\\CUDA\\include;D:\\CUDA\\lib;D:\\CUDA\\lib\\x64;D:\\CUDA\\nvvm\\bin", "PATHEXT": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL", "PORT": "3001", "POWERSHELL_DISTRIBUTION_CHANNEL": "MSI:Windows 10 Pro", "PROCESSOR_ARCHITECTURE": "AMD64", "PROCESSOR_IDENTIFIER": "AMD64 Family 23 Model 113 Stepping 0, AuthenticAMD", "PROCESSOR_LEVEL": "23", "PROCESSOR_REVISION": "7100", "ProgramData": "C:\\ProgramData", "ProgramFiles": "C:\\Program Files", "ProgramFiles(x86)": "C:\\Program Files (x86)", "ProgramW6432": "C:\\Program Files", "PROMPT": "$P$G", "PSModulePath": "F:\\Documentos\\PowerShell\\Modules;C:\\Program Files\\PowerShell\\Modules;d:\\program files\\powershell\\7\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules", "PUBLIC": "C:\\Users\\<USER>", "QUERY_TIMEOUT": "30000", "SSE_PORT": "3003", "SystemDrive": "C:", "SystemRoot": "C:\\WINDOWS", "TEMP": "C:\\Users\\<USER>\\AppData\\Local\\Temp", "TF_ENABLE_ONEDNN_OPTS": "0", "TMP": "C:\\Users\\<USER>\\AppData\\Local\\Temp", "TORIO_USE_FFMPEG_VERSION": "6", "TRANSPORT_TYPE": "stdio", "USERDOMAIN": "DESKTOP-O4OOE4K", "USERDOMAIN_ROAMINGPROFILE": "DESKTOP-O4OOE4K", "USERNAME": "jhonn", "USERPROFILE": "C:\\Users\\<USER>", "VBOX_MSI_INSTALL_PATH": "D:\\Program Files\\Oracle\\VirtualBox\\", "windir": "C:\\WINDOWS", "WS_PORT": "3002", "__PSLockDownPolicy": "0"}