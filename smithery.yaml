# Smithery configuration file: https://smithery.ai/docs/config#smitheryyaml

startCommand:
  type: stdio
  configSchema:
    # JSON Schema defining the configuration options for the MCP.
    type: object
    required:
      - firebirdDatabase
    properties:
      firebirdHost:
        type: string
        default: localhost
        description: Hostname of the Firebird database server
      firebirdPort:
        type: number
        default: 3050
        description: Port for the Firebird database
      firebirdDatabase:
        type: string
        description: Absolute path to the Firebird database file
      firebirdUser:
        type: string
        default: SYSDBA
        description: Database user
      firebirdPassword:
        type: string
        default: masterkey
        description: Database password
      transportType:
        type: string
        default: stdio
        description: "Transport type: 'stdio' for local or 'sse' for server-sent events"
      ssePort:
        type: number
        default: 3003
        description: Port for SSE transport (if transportType is 'sse')
      logLevel:
        type: string
        default: info
        description: Logging level (debug, info, warn, error)
  commandFunction:
    # A JS function that produces the CLI command based on the given config to start the MCP on stdio.
    |-
    (config) => {
      // Build command line arguments to pass directly to the CLI
      const args = ['dist/cli.js'];

      // Add all configuration parameters as command line arguments
      if (config.firebirdHost) args.push('--host', config.firebirdHost);
      if (config.firebirdPort) args.push('--port', String(config.firebirdPort));
      if (config.firebirdDatabase) args.push('--database', config.firebirdDatabase);
      if (config.firebirdUser) args.push('--user', config.firebirdUser);
      if (config.firebirdPassword) args.push('--password', config.firebirdPassword);

      // Set transport type if specified
      if (config.transportType === 'sse') {
        args.push('--transport-type', 'sse');
        if (config.ssePort) args.push('--sse-port', String(config.ssePort));
      }

      // Set log level if specified
      if (config.logLevel) args.push('--log-level', config.logLevel);

      // Also set environment variables as a fallback
      const env = Object.assign({}, process.env, {
        FIREBIRD_HOST: config.firebirdHost,
        FIREBIRD_PORT: String(config.firebirdPort),
        FIREBIRD_DATABASE: config.firebirdDatabase,
        FIREBIRD_USER: config.firebirdUser,
        FIREBIRD_PASSWORD: config.firebirdPassword,
        TRANSPORT_TYPE: config.transportType,
        LOG_LEVEL: config.logLevel
      });

      // If transport is sse, add SSE_PORT
      if(config.transportType === 'sse') {
        env.SSE_PORT = String(config.ssePort);
      }

      return {
        command: 'node',
        args,
        env
      };
    }
  exampleConfig:
    firebirdHost: ***********
    firebirdPort: 3050
    firebirdDatabase: F:\\Proyectos\\SAI\\EMPLOYEE.FDB
    firebirdUser: SYSDBA
    firebirdPassword: masterkey
    transportType: stdio
    ssePort: 3003
    logLevel: info
