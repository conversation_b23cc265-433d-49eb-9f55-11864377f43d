{"tag_name": "v2.0.4", "target_commitish": "main", "name": "MCP Firebird v2.0.4", "body": "# MCP Firebird v2.0.4 Release Notes\n\nWe are excited to announce the release of MCP Firebird v2.0.4, a significant update to our Model Context Protocol implementation for Firebird databases. This stable release brings several important improvements and new features that enhance both functionality and user experience.\n\n## Key Improvements\n\n### 🚀 Server-Sent Events (SSE) Support\n- Added full support for Server-Sent Events (SSE) as a transport mechanism\n- Implemented SSE server capabilities for real-time data streaming\n- Added comprehensive documentation and examples for SSE integration\n\n### 🔧 Robust Command-Line Parameter Handling\n- Enhanced the command-line interface with improved parameter handling\n- Fixed issues with environment variable processing\n- Ensured seamless compatibility with both NPX parameters and environment variables\n\n### 🔍 Enhanced Database Tools\n- Improved database table listing functionality\n- Enhanced query execution capabilities\n- Added better error handling and reporting for database operations\n\n### 📊 Performance Optimizations\n- Optimized database connection management\n- Improved query performance and resource utilization\n- Enhanced overall server responsiveness\n\n### 🔄 Claude Desktop Integration\n- Fixed compatibility issues with Claude Desktop\n- Ensured reliable operation when used as a context provider for Claude AI\n- Streamlined configuration process for AI integrations\n\n### 📚 Expanded Documentation\n- Added comprehensive documentation for all new features\n- Included detailed examples for common use cases\n- Updated installation and configuration guides\n\n## Breaking Changes\nNone. This version maintains backward compatibility with previous versions.\n\n## Upgrading\nTo upgrade to MCP Firebird v2.0.4, simply run:\n```\nnpm install mcp-firebird@latest\n```\n\nOr use it directly with npx:\n```\nnpx mcp-firebird --database your_database.fdb --user SYSDBA --password masterkey\n```\n\n## Feedback\nWe welcome your feedback and suggestions for future improvements. Please report any issues on our GitHub repository.\n\nThank you for using MCP Firebird!", "draft": false, "prerelease": false, "generate_release_notes": false}